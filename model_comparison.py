#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红酒分类模型对比分析
Wine Classification Model Comparison Analysis

专门用于生成各种模型对比图表的模块
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.datasets import load_wine
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neighbors import KNeighborsClassifier
from sklearn.naive_bayes import GaussianNB
from sklearn.tree import DecisionTreeClassifier
from sklearn.neural_network import MLPClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score
import time
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class ModelComparison:
    """模型对比分析类"""
    
    def __init__(self):
        self.models = {}
        self.results = {}
        self.scaler = StandardScaler()
        
    def load_and_prepare_data(self):
        """加载和准备数据"""
        print("加载红酒数据集...")

        # 定义特征名称
        self.feature_names = [
            'alcohol', 'malic_acid', 'ash', 'alcalinity_of_ash', 'magnesium',
            'total_phenols', 'flavanoids', 'nonflavanoid_phenols', 'proanthocyanins',
            'color_intensity', 'hue', 'od280_od315_of_diluted_wines', 'proline'
        ]

        # 定义类别名称
        self.target_names = ['class_1', 'class_2', 'class_3']

        try:
            # 读取本地wine数据文件
            wine_df = pd.read_csv('wine/wine.data', header=None)

            # 第一列是类别标签，其余是特征
            y = wine_df.iloc[:, 0].values - 1  # 转换为0,1,2
            X = wine_df.iloc[:, 1:].values

        except FileNotFoundError:
            print("未找到wine/wine.data文件，使用sklearn内置数据集...")
            # 备用方案：使用sklearn内置数据集
            wine_data = load_wine()
            X, y = wine_data.data, wine_data.target
            self.feature_names = wine_data.feature_names
            self.target_names = wine_data.target_names

        # 划分数据集
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )

        # 标准化
        X_train_scaled = self.scaler.fit_transform(X_train)
        X_test_scaled = self.scaler.transform(X_test)

        self.X_train, self.X_test = X_train_scaled, X_test_scaled
        self.y_train, self.y_test = y_train, y_test

        print(f"训练集: {X_train.shape}, 测试集: {X_test.shape}")
        
    def define_models(self):
        """定义所有要比较的模型"""
        self.models = {
            'SVM(线性核)': SVC(kernel='linear', random_state=42),
            'SVM(RBF核)': SVC(kernel='rbf', random_state=42),
            '随机森林': RandomForestClassifier(n_estimators=100, random_state=42),
            '梯度提升': GradientBoostingClassifier(random_state=42),
            'K近邻': KNeighborsClassifier(n_neighbors=5),
            '逻辑回归': LogisticRegression(random_state=42, max_iter=1000),
            '决策树': DecisionTreeClassifier(random_state=42),
            '朴素贝叶斯': GaussianNB(),
            '神经网络': MLPClassifier(hidden_layer_sizes=(100,), random_state=42, max_iter=1000)
        }
        
    def train_and_evaluate_models(self):
        """训练和评估所有模型"""
        print("开始训练和评估模型...")
        
        for name, model in self.models.items():
            print(f"训练 {name}...")
            
            # 记录训练时间
            start_time = time.time()
            model.fit(self.X_train, self.y_train)
            training_time = time.time() - start_time
            
            # 预测
            y_pred = model.predict(self.X_test)
            
            # 计算各种指标
            accuracy = accuracy_score(self.y_test, y_pred)
            precision = precision_score(self.y_test, y_pred, average='weighted')
            recall = recall_score(self.y_test, y_pred, average='weighted')
            f1 = f1_score(self.y_test, y_pred, average='weighted')
            
            # 交叉验证
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=5)
            
            self.results[name] = {
                'model': model,
                'accuracy': accuracy,
                'precision': precision,
                'recall': recall,
                'f1_score': f1,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'training_time': training_time,
                'predictions': y_pred
            }
            
            print(f"{name} - 准确率: {accuracy:.4f}")
            
    def plot_accuracy_comparison(self):
        """绘制模型准确率对比图"""
        model_names = list(self.results.keys())
        accuracies = [self.results[name]['accuracy'] for name in model_names]
        training_times = [self.results[name]['training_time'] for name in model_names]
        
        # 创建双轴图
        fig, ax1 = plt.subplots(figsize=(14, 8))
        
        # 准确率柱状图
        bars = ax1.bar(model_names, accuracies, color='steelblue', alpha=0.7, label='准确率')
        ax1.set_ylabel('准确率', fontsize=12)
        ax1.set_ylim(0, 1.05)
        ax1.tick_params(axis='x', rotation=45)
        
        # 在柱子上添加数值
        for bar, acc in zip(bars, accuracies):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{acc:.4f}', ha='center', va='bottom', fontsize=10)
        
        # 训练时间折线图
        ax2 = ax1.twinx()
        line = ax2.plot(model_names, training_times, color='red', marker='o', 
                       linewidth=2, markersize=6, label='训练时间')
        ax2.set_ylabel('训练时间(秒)', fontsize=12, color='red')
        ax2.tick_params(axis='y', labelcolor='red')
        
        plt.title('模型性能综合对比', fontsize=16, fontweight='bold')
        
        # 添加图例
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
        
        plt.tight_layout()
        plt.savefig('model_performance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_training_time_comparison(self):
        """绘制各模型预测时间对比图"""
        model_names = list(self.results.keys())
        training_times = [self.results[name]['training_time'] for name in model_names]
        
        plt.figure(figsize=(12, 6))
        bars = plt.bar(model_names, training_times, color='lightgreen', alpha=0.8)
        plt.title('各模型预测时间对比', fontsize=14, fontweight='bold')
        plt.ylabel('预测时间(秒)', fontsize=12)
        plt.xticks(rotation=45)
        
        # 在柱子上添加数值
        for bar, time_val in zip(bars, training_times):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + max(training_times)*0.01, 
                    f'{time_val:.4f}', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        plt.savefig('model_training_time_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_cross_validation_comparison(self):
        """绘制交叉验证准确率对比图"""
        model_names = list(self.results.keys())
        cv_means = [self.results[name]['cv_mean'] for name in model_names]
        cv_stds = [self.results[name]['cv_std'] for name in model_names]
        
        plt.figure(figsize=(12, 8))
        
        # 创建箱线图数据
        cv_data = []
        labels = []
        for name in model_names:
            model = self.results[name]['model']
            cv_scores = cross_val_score(model, self.X_train, self.y_train, cv=5)
            cv_data.append(cv_scores)
            labels.append(name)
        
        # 绘制箱线图
        box_plot = plt.boxplot(cv_data, labels=labels, patch_artist=True)
        
        # 设置颜色
        colors = ['lightblue', 'lightgreen', 'lightcoral', 'lightyellow', 
                 'lightpink', 'lightgray', 'lightcyan', 'wheat', 'lavender']
        for patch, color in zip(box_plot['boxes'], colors):
            patch.set_facecolor(color)
        
        plt.title('各模型交叉验证准确率对比', fontsize=14, fontweight='bold')
        plt.ylabel('准确率', fontsize=12)
        plt.xticks(rotation=45)
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('cross_validation_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_comprehensive_metrics(self):
        """绘制综合指标对比图"""
        model_names = list(self.results.keys())
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']
        metric_names = ['准确率', '精确率', '召回率', 'F1分数']
        
        # 准备数据
        data = []
        for metric in metrics:
            data.append([self.results[name][metric] for name in model_names])
        
        # 创建子图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        axes = axes.ravel()
        
        colors = ['skyblue', 'lightgreen', 'lightcoral', 'gold']
        
        for i, (metric_data, metric_name, color) in enumerate(zip(data, metric_names, colors)):
            ax = axes[i]
            bars = ax.bar(model_names, metric_data, color=color, alpha=0.8)
            ax.set_title(f'{metric_name}对比', fontsize=12, fontweight='bold')
            ax.set_ylabel(metric_name, fontsize=10)
            ax.set_ylim(0, 1.05)
            ax.tick_params(axis='x', rotation=45)
            
            # 添加数值标签
            for bar, value in zip(bars, metric_data):
                ax.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                       f'{value:.3f}', ha='center', va='bottom', fontsize=9)
        
        plt.suptitle('模型综合性能指标对比', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('comprehensive_metrics_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def plot_radar_chart(self):
        """绘制雷达图对比前5个模型"""
        # 选择前5个准确率最高的模型
        sorted_models = sorted(self.results.items(), 
                             key=lambda x: x[1]['accuracy'], reverse=True)[:5]
        
        metrics = ['accuracy', 'precision', 'recall', 'f1_score']
        metric_labels = ['准确率', '精确率', '召回率', 'F1分数']
        
        # 设置雷达图
        angles = np.linspace(0, 2 * np.pi, len(metrics), endpoint=False).tolist()
        angles += angles[:1]  # 闭合图形
        
        fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))
        
        colors = ['red', 'blue', 'green', 'orange', 'purple']
        
        for i, (name, results) in enumerate(sorted_models):
            values = [results[metric] for metric in metrics]
            values += values[:1]  # 闭合图形
            
            ax.plot(angles, values, 'o-', linewidth=2, label=name, color=colors[i])
            ax.fill(angles, values, alpha=0.25, color=colors[i])
        
        ax.set_xticks(angles[:-1])
        ax.set_xticklabels(metric_labels)
        ax.set_ylim(0, 1)
        ax.set_title('前5名模型性能雷达图', size=16, fontweight='bold', pad=20)
        ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
        
        plt.tight_layout()
        plt.savefig('model_radar_chart.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_performance_report(self):
        """生成性能报告"""
        print("\n" + "="*60)
        print("红酒分类模型性能详细报告")
        print("="*60)
        
        # 按准确率排序
        sorted_results = sorted(self.results.items(), 
                              key=lambda x: x[1]['accuracy'], reverse=True)
        
        print(f"{'排名':<4} {'模型名称':<12} {'准确率':<8} {'精确率':<8} {'召回率':<8} {'F1分数':<8} {'训练时间(秒)':<12}")
        print("-" * 70)
        
        for rank, (name, results) in enumerate(sorted_results, 1):
            print(f"{rank:<4} {name:<12} {results['accuracy']:.4f}   "
                  f"{results['precision']:.4f}   {results['recall']:.4f}   "
                  f"{results['f1_score']:.4f}   {results['training_time']:.4f}")
        
        # 最佳模型
        best_model_name = sorted_results[0][0]
        best_results = sorted_results[0][1]
        
        print(f"\n最佳模型: {best_model_name}")
        print(f"最高准确率: {best_results['accuracy']:.4f}")
        print(f"交叉验证准确率: {best_results['cv_mean']:.4f} ± {best_results['cv_std']:.4f}")
        
    def run_complete_comparison(self):
        """运行完整的模型对比分析"""
        print("开始红酒分类模型对比分析...")
        print("="*50)
        
        # 1. 加载数据
        self.load_and_prepare_data()
        
        # 2. 定义模型
        self.define_models()
        
        # 3. 训练和评估
        self.train_and_evaluate_models()
        
        # 4. 生成各种对比图
        self.plot_accuracy_comparison()
        self.plot_training_time_comparison()
        self.plot_cross_validation_comparison()
        self.plot_comprehensive_metrics()
        self.plot_radar_chart()
        
        # 5. 生成报告
        self.generate_performance_report()
        
        print("\n模型对比分析完成！所有图表已保存。")

if __name__ == "__main__":
    # 运行模型对比分析
    comparison = ModelComparison()
    comparison.run_complete_comparison()
