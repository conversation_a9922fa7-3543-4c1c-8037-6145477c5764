#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版红酒数据分析
Simple Wine Data Analysis

不依赖复杂库的基础数据分析
"""

import csv
import math

def load_wine_data():
    """加载wine数据"""
    print("正在加载wine数据...")
    
    # 特征名称
    feature_names = [
        'alcohol', 'malic_acid', 'ash', 'alcalinity_of_ash', 'magnesium',
        'total_phenols', 'flavanoids', 'nonflavanoid_phenols', 'proanthocyanins',
        'color_intensity', 'hue', 'od280_od315_of_diluted_wines', 'proline'
    ]
    
    # 类别名称
    target_names = ['class_1', 'class_2', 'class_3']
    
    data = []
    labels = []
    
    try:
        with open('wine/wine.data', 'r') as f:
            for line in f:
                line = line.strip()
                if line:
                    values = line.split(',')
                    # 第一个值是类别标签
                    label = int(values[0]) - 1  # 转换为0,1,2
                    # 其余是特征值
                    features = [float(v) for v in values[1:]]
                    
                    labels.append(label)
                    data.append(features)
        
        print(f"成功加载数据: {len(data)} 个样本, {len(data[0])} 个特征")
        
        # 统计类别分布
        class_counts = [0, 0, 0]
        for label in labels:
            class_counts[label] += 1
        
        print("类别分布:")
        for i, count in enumerate(class_counts):
            print(f"  {target_names[i]}: {count} 个样本")
        
        return data, labels, feature_names, target_names
        
    except FileNotFoundError:
        print("错误: 未找到wine/wine.data文件")
        return None, None, None, None
    except Exception as e:
        print(f"读取数据时出错: {e}")
        return None, None, None, None

def calculate_statistics(data, labels, feature_names):
    """计算基本统计信息"""
    print("\n=== 基本统计信息 ===")
    
    n_samples = len(data)
    n_features = len(data[0])
    
    # 计算每个特征的统计信息
    for i, feature_name in enumerate(feature_names):
        values = [row[i] for row in data]
        
        # 计算基本统计量
        mean_val = sum(values) / len(values)
        min_val = min(values)
        max_val = max(values)
        
        # 计算标准差
        variance = sum((x - mean_val) ** 2 for x in values) / len(values)
        std_val = math.sqrt(variance)
        
        print(f"{feature_name}:")
        print(f"  均值: {mean_val:.3f}")
        print(f"  标准差: {std_val:.3f}")
        print(f"  最小值: {min_val:.3f}")
        print(f"  最大值: {max_val:.3f}")
        print()

def analyze_by_class(data, labels, feature_names, target_names):
    """按类别分析特征"""
    print("\n=== 按类别分析特征 ===")
    
    # 按类别分组数据
    class_data = [[], [], []]
    for i, label in enumerate(labels):
        class_data[label].append(data[i])
    
    # 分析每个特征在不同类别中的表现
    for feat_idx, feature_name in enumerate(feature_names):
        print(f"\n{feature_name}:")
        
        for class_idx, class_name in enumerate(target_names):
            if class_data[class_idx]:  # 确保该类别有数据
                values = [row[feat_idx] for row in class_data[class_idx]]
                mean_val = sum(values) / len(values)
                min_val = min(values)
                max_val = max(values)
                
                print(f"  {class_name}: 均值={mean_val:.3f}, 范围=[{min_val:.3f}, {max_val:.3f}]")

def find_important_features(data, labels, feature_names, target_names):
    """寻找重要特征（基于类别间差异）"""
    print("\n=== 特征重要性分析 ===")
    
    # 按类别分组数据
    class_data = [[], [], []]
    for i, label in enumerate(labels):
        class_data[label].append(data[i])
    
    feature_importance = []
    
    for feat_idx, feature_name in enumerate(feature_names):
        # 计算每个类别的均值
        class_means = []
        for class_idx in range(3):
            if class_data[class_idx]:
                values = [row[feat_idx] for row in class_data[class_idx]]
                mean_val = sum(values) / len(values)
                class_means.append(mean_val)
        
        # 计算类别间方差（作为重要性指标）
        if len(class_means) == 3:
            overall_mean = sum(class_means) / 3
            between_class_variance = sum((mean - overall_mean) ** 2 for mean in class_means) / 3
            feature_importance.append((feature_name, between_class_variance))
    
    # 按重要性排序
    feature_importance.sort(key=lambda x: x[1], reverse=True)
    
    print("特征重要性排序（基于类别间差异）:")
    for i, (feature_name, importance) in enumerate(feature_importance):
        print(f"{i+1:2d}. {feature_name}: {importance:.3f}")

def simple_classification_test(data, labels, feature_names):
    """简单的分类测试（使用最近邻方法）"""
    print("\n=== 简单分类测试 ===")
    
    n_samples = len(data)
    correct_predictions = 0
    
    # 留一法交叉验证
    for test_idx in range(n_samples):
        test_sample = data[test_idx]
        test_label = labels[test_idx]
        
        # 找到最近的邻居
        min_distance = float('inf')
        predicted_label = -1
        
        for train_idx in range(n_samples):
            if train_idx != test_idx:
                train_sample = data[train_idx]
                train_label = labels[train_idx]
                
                # 计算欧氏距离
                distance = sum((test_sample[i] - train_sample[i]) ** 2 for i in range(len(test_sample)))
                distance = math.sqrt(distance)
                
                if distance < min_distance:
                    min_distance = distance
                    predicted_label = train_label
        
        if predicted_label == test_label:
            correct_predictions += 1
    
    accuracy = correct_predictions / n_samples
    print(f"最近邻分类准确率: {accuracy:.3f} ({correct_predictions}/{n_samples})")

def generate_simple_report(data, labels, feature_names, target_names):
    """生成简单报告"""
    print("\n" + "="*60)
    print("红酒数据分析报告")
    print("="*60)
    
    print(f"数据集信息:")
    print(f"  样本数量: {len(data)}")
    print(f"  特征数量: {len(feature_names)}")
    print(f"  类别数量: {len(target_names)}")
    
    # 类别分布
    class_counts = [0, 0, 0]
    for label in labels:
        class_counts[label] += 1
    
    print(f"\n类别分布:")
    for i, count in enumerate(class_counts):
        percentage = (count / len(labels)) * 100
        print(f"  {target_names[i]}: {count} 个样本 ({percentage:.1f}%)")
    
    print(f"\n数据质量:")
    print(f"  缺失值: 0 个")
    print(f"  数据类型: 全部为数值型")
    
    print(f"\n建议:")
    print(f"  1. 数据质量良好，无需特殊处理")
    print(f"  2. 建议进行特征标准化")
    print(f"  3. 可以尝试多种机器学习算法")
    print(f"  4. 数据集适合作为分类算法的测试基准")

def main():
    """主函数"""
    print("红酒数据简单分析工具")
    print("="*40)
    
    # 加载数据
    data, labels, feature_names, target_names = load_wine_data()
    
    if data is None:
        print("数据加载失败，程序退出。")
        return
    
    # 基本统计分析
    calculate_statistics(data, labels, feature_names)
    
    # 按类别分析
    analyze_by_class(data, labels, feature_names, target_names)
    
    # 特征重要性分析
    find_important_features(data, labels, feature_names, target_names)
    
    # 简单分类测试
    simple_classification_test(data, labels, feature_names)
    
    # 生成报告
    generate_simple_report(data, labels, feature_names, target_names)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()
