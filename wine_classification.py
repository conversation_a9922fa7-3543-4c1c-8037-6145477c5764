#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于支持向量机的红酒分类研究
Wine Classification using Support Vector Machine

作者: [您的姓名]
日期: 2025年6月
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import csv
from sklearn.datasets import load_wine
from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
from sklearn.preprocessing import StandardScaler, LabelEncoder
from sklearn.svm import SVC
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.neighbors import KNeighborsClassifier
from sklearn.metrics import classification_report, confusion_matrix, accuracy_score
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class WineClassifier:
    """红酒分类器类"""
    
    def __init__(self):
        self.models = {}
        self.scaler = StandardScaler()
        self.pca = PCA()
        self.X_train = None
        self.X_test = None
        self.y_train = None
        self.y_test = None
        self.feature_names = None
        self.target_names = None
        
    def load_data(self):
        """加载红酒数据集"""
        print("正在加载红酒数据集...")

        # 定义特征名称
        self.feature_names = [
            'alcohol', 'malic_acid', 'ash', 'alcalinity_of_ash', 'magnesium',
            'total_phenols', 'flavanoids', 'nonflavanoid_phenols', 'proanthocyanins',
            'color_intensity', 'hue', 'od280_od315_of_diluted_wines', 'proline'
        ]

        # 定义类别名称
        self.target_names = ['class_1', 'class_2', 'class_3']

        try:
            # 尝试使用pandas读取
            try:
                wine_df = pd.read_csv('wine/wine.data', header=None)
                # 第一列是类别标签，其余是特征
                self.y = wine_df.iloc[:, 0].values - 1  # 转换为0,1,2
                self.X = wine_df.iloc[:, 1:].values
                print("使用pandas成功读取wine数据文件")

            except (ImportError, NameError):
                # pandas不可用，使用csv模块
                print("pandas不可用，使用csv模块读取数据...")
                data_rows = []
                labels = []

                with open('wine/wine.data', 'r') as f:
                    csv_reader = csv.reader(f)
                    for row in csv_reader:
                        if row:  # 跳过空行
                            labels.append(int(row[0]) - 1)  # 转换为0,1,2
                            data_rows.append([float(x) for x in row[1:]])

                self.y = np.array(labels)
                self.X = np.array(data_rows)
                print("使用csv模块成功读取wine数据文件")

            # 创建DataFrame便于分析
            self.df = pd.DataFrame(self.X, columns=self.feature_names)
            self.df['target'] = self.y
            self.df['target_name'] = [self.target_names[i] for i in self.y]

            print(f"数据集形状: {self.df.shape}")
            print(f"特征数量: {len(self.feature_names)}")
            print(f"类别数量: {len(self.target_names)}")

            # 计算类别分布
            class_counts = {}
            for label in self.y:
                class_counts[label] = class_counts.get(label, 0) + 1
            print(f"类别分布: {class_counts}")

        except FileNotFoundError:
            print("未找到wine/wine.data文件，使用sklearn内置数据集...")
            # 备用方案：使用sklearn内置数据集
            wine_data = load_wine()
            self.X = wine_data.data
            self.y = wine_data.target
            self.feature_names = wine_data.feature_names
            self.target_names = wine_data.target_names

            # 创建DataFrame便于分析
            self.df = pd.DataFrame(self.X, columns=self.feature_names)
            self.df['target'] = self.y
            self.df['target_name'] = [self.target_names[i] for i in self.y]

            print(f"数据集形状: {self.df.shape}")
            print(f"特征数量: {len(self.feature_names)}")
            print(f"类别数量: {len(self.target_names)}")

            # 计算类别分布
            class_counts = {}
            for label in self.y:
                class_counts[label] = class_counts.get(label, 0) + 1
            print(f"类别分布: {class_counts}")
        
    def exploratory_data_analysis(self):
        """探索性数据分析"""
        print("\n开始探索性数据分析...")
        
        # 1. 特征分布图
        plt.figure(figsize=(20, 15))
        for i, feature in enumerate(self.feature_names[:12]):  # 显示前12个特征
            plt.subplot(3, 4, i+1)
            for target in range(len(self.target_names)):
                data = self.df[self.df['target'] == target][feature]
                plt.hist(data, alpha=0.7, label=self.target_names[target], bins=20)
            plt.title(f'{feature}分布')
            plt.xlabel(feature)
            plt.ylabel('频次')
            plt.legend()
        plt.tight_layout()
        plt.savefig('feature_distributions.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. 箱线图
        plt.figure(figsize=(20, 15))
        for i, feature in enumerate(self.feature_names[:12]):
            plt.subplot(3, 4, i+1)
            data_to_plot = [self.df[self.df['target'] == target][feature] 
                           for target in range(len(self.target_names))]
            plt.boxplot(data_to_plot, labels=self.target_names)
            plt.title(f'{feature}箱线图')
            plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('feature_boxplots.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 3. 相关性热力图
        plt.figure(figsize=(15, 12))
        correlation_matrix = self.df[self.feature_names].corr()
        sns.heatmap(correlation_matrix, annot=False, cmap='coolwarm', center=0)
        plt.title('特征相关性热力图')
        plt.tight_layout()
        plt.savefig('feature_correlation.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 4. 特征对比图
        plt.figure(figsize=(15, 10))
        sns.pairplot(self.df[['alcohol', 'flavanoids', 'color_intensity', 'target_name']], 
                    hue='target_name', diag_kind='hist')
        plt.savefig('feature_pairplot.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def preprocess_data(self):
        """数据预处理"""
        print("\n开始数据预处理...")
        
        # 划分训练集和测试集
        self.X_train, self.X_test, self.y_train, self.y_test = train_test_split(
            self.X, self.y, test_size=0.3, random_state=42, stratify=self.y
        )
        
        # 标准化
        self.X_train_scaled = self.scaler.fit_transform(self.X_train)
        self.X_test_scaled = self.scaler.transform(self.X_test)
        
        print(f"训练集大小: {self.X_train.shape}")
        print(f"测试集大小: {self.X_test.shape}")
        
    def train_models(self):
        """训练多个模型"""
        print("\n开始训练模型...")
        
        # 定义模型
        models = {
            'SVM': SVC(random_state=42),
            'Random Forest': RandomForestClassifier(random_state=42),
            'Logistic Regression': LogisticRegression(random_state=42, max_iter=1000),
            'KNN': KNeighborsClassifier()
        }
        
        # 训练模型并记录性能
        results = {}
        training_times = {}
        
        for name, model in models.items():
            print(f"训练 {name}...")
            
            # 记录训练时间
            import time
            start_time = time.time()
            
            # 训练模型
            model.fit(self.X_train_scaled, self.y_train)
            
            end_time = time.time()
            training_times[name] = end_time - start_time
            
            # 预测
            y_pred = model.predict(self.X_test_scaled)
            
            # 计算准确率
            accuracy = accuracy_score(self.y_test, y_pred)
            
            # 交叉验证
            cv_scores = cross_val_score(model, self.X_train_scaled, self.y_train, cv=5)
            
            results[name] = {
                'model': model,
                'accuracy': accuracy,
                'cv_mean': cv_scores.mean(),
                'cv_std': cv_scores.std(),
                'predictions': y_pred,
                'training_time': training_times[name]
            }
            
            print(f"{name} - 准确率: {accuracy:.4f}, 交叉验证: {cv_scores.mean():.4f}±{cv_scores.std():.4f}")
        
        self.models = results
        return results
        
    def optimize_svm(self):
        """优化SVM模型"""
        print("\n开始SVM参数优化...")
        
        # 参数网格
        param_grid = {
            'C': [0.1, 1, 10, 100],
            'gamma': ['scale', 'auto', 0.001, 0.01, 0.1, 1],
            'kernel': ['rbf', 'poly', 'sigmoid']
        }
        
        # 网格搜索
        svm = SVC(random_state=42)
        grid_search = GridSearchCV(svm, param_grid, cv=5, scoring='accuracy', n_jobs=1)
        grid_search.fit(self.X_train_scaled, self.y_train)
        
        # 最佳模型
        best_svm = grid_search.best_estimator_
        y_pred_best = best_svm.predict(self.X_test_scaled)
        best_accuracy = accuracy_score(self.y_test, y_pred_best)
        
        print(f"最佳SVM参数: {grid_search.best_params_}")
        print(f"最佳SVM准确率: {best_accuracy:.4f}")
        
        # 更新SVM结果
        self.models['SVM (Optimized)'] = {
            'model': best_svm,
            'accuracy': best_accuracy,
            'predictions': y_pred_best,
            'best_params': grid_search.best_params_
        }
        
        return best_svm
        
    def visualize_results(self):
        """可视化结果"""
        print("\n生成可视化结果...")
        
        # 1. 模型准确率比较
        model_names = list(self.models.keys())
        accuracies = [self.models[name]['accuracy'] for name in model_names]
        
        plt.figure(figsize=(10, 6))
        bars = plt.bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'])
        plt.title('模型准确率比较')
        plt.ylabel('准确率')
        plt.ylim(0, 1)
        
        # 在柱子上添加数值
        for bar, acc in zip(bars, accuracies):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01, 
                    f'{acc:.3f}', ha='center', va='bottom')
        
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.savefig('model_accuracy_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. 训练时间比较
        if hasattr(self, 'models') and 'training_time' in list(self.models.values())[0]:
            training_times = [self.models[name].get('training_time', 0) for name in model_names[:-1]]  # 排除优化后的SVM
            
            plt.figure(figsize=(10, 6))
            plt.bar(model_names[:-1], training_times, color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
            plt.title('模型训练时间比较')
            plt.ylabel('训练时间 (秒)')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig('model_training_time.png', dpi=300, bbox_inches='tight')
            plt.show()
        
        # 3. 混淆矩阵 (使用最佳SVM)
        best_model_name = max(self.models.keys(), key=lambda x: self.models[x]['accuracy'])
        best_predictions = self.models[best_model_name]['predictions']
        
        plt.figure(figsize=(8, 6))
        cm = confusion_matrix(self.y_test, best_predictions)
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', 
                   xticklabels=self.target_names, yticklabels=self.target_names)
        plt.title(f'混淆矩阵 - {best_model_name}')
        plt.ylabel('真实标签')
        plt.xlabel('预测标签')
        plt.tight_layout()
        plt.savefig('confusion_matrix.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def feature_importance_analysis(self):
        """特征重要性分析"""
        print("\n进行特征重要性分析...")
        
        # 使用随机森林获取特征重要性
        rf_model = self.models['Random Forest']['model']
        feature_importance = rf_model.feature_importances_
        
        # 排序
        indices = np.argsort(feature_importance)[::-1]
        
        plt.figure(figsize=(12, 8))
        plt.title('特征重要性排序 (随机森林)')
        plt.bar(range(len(feature_importance)), feature_importance[indices])
        plt.xticks(range(len(feature_importance)), 
                  [self.feature_names[i] for i in indices], rotation=90)
        plt.ylabel('重要性')
        plt.tight_layout()
        plt.savefig('feature_importance.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印前10个重要特征
        print("前10个重要特征:")
        for i in range(min(10, len(feature_importance))):
            idx = indices[i]
            print(f"{i+1}. {self.feature_names[idx]}: {feature_importance[idx]:.4f}")
            
    def dimensionality_reduction(self):
        """降维可视化"""
        print("\n进行降维可视化...")
        
        # PCA降维
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(self.X_train_scaled)
        
        plt.figure(figsize=(12, 5))
        
        # PCA可视化
        plt.subplot(1, 2, 1)
        colors = ['red', 'green', 'blue']
        for i, target_name in enumerate(self.target_names):
            mask = self.y_train == i
            plt.scatter(X_pca[mask, 0], X_pca[mask, 1], 
                       c=colors[i], label=target_name, alpha=0.7)
        plt.title('PCA降维可视化')
        plt.xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%} variance)')
        plt.ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%} variance)')
        plt.legend()
        
        # t-SNE降维
        tsne = TSNE(n_components=2, random_state=42)
        X_tsne = tsne.fit_transform(self.X_train_scaled)
        
        plt.subplot(1, 2, 2)
        for i, target_name in enumerate(self.target_names):
            mask = self.y_train == i
            plt.scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                       c=colors[i], label=target_name, alpha=0.7)
        plt.title('t-SNE降维可视化')
        plt.xlabel('t-SNE 1')
        plt.ylabel('t-SNE 2')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('tsne_visualization.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # PCA成分分析
        plt.figure(figsize=(10, 6))
        pca_full = PCA()
        pca_full.fit(self.X_train_scaled)
        
        plt.subplot(1, 2, 1)
        plt.plot(range(1, len(pca_full.explained_variance_ratio_) + 1), 
                pca_full.explained_variance_ratio_, 'bo-')
        plt.title('PCA成分方差解释比例')
        plt.xlabel('主成分')
        plt.ylabel('方差解释比例')
        
        plt.subplot(1, 2, 2)
        plt.plot(range(1, len(pca_full.explained_variance_ratio_) + 1), 
                np.cumsum(pca_full.explained_variance_ratio_), 'ro-')
        plt.title('PCA累积方差解释比例')
        plt.xlabel('主成分')
        plt.ylabel('累积方差解释比例')
        plt.axhline(y=0.95, color='k', linestyle='--', alpha=0.7, label='95%')
        plt.legend()
        
        plt.tight_layout()
        plt.savefig('pca_components.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def generate_comprehensive_report(self):
        """生成综合报告"""
        print("\n生成综合性能报告...")
        
        # 创建综合比较图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 准确率比较
        model_names = list(self.models.keys())
        accuracies = [self.models[name]['accuracy'] for name in model_names]
        
        axes[0, 0].bar(model_names, accuracies, color=['skyblue', 'lightgreen', 'lightcoral', 'gold', 'plum'])
        axes[0, 0].set_title('模型准确率比较')
        axes[0, 0].set_ylabel('准确率')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 交叉验证分数
        cv_means = [self.models[name].get('cv_mean', 0) for name in model_names if 'cv_mean' in self.models[name]]
        cv_stds = [self.models[name].get('cv_std', 0) for name in model_names if 'cv_std' in self.models[name]]
        cv_names = [name for name in model_names if 'cv_mean' in self.models[name]]
        
        axes[0, 1].bar(cv_names, cv_means, yerr=cv_stds, capsize=5, 
                      color=['skyblue', 'lightgreen', 'lightcoral', 'gold'])
        axes[0, 1].set_title('交叉验证准确率')
        axes[0, 1].set_ylabel('准确率')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 特征重要性 (前10个)
        rf_model = self.models['Random Forest']['model']
        feature_importance = rf_model.feature_importances_
        indices = np.argsort(feature_importance)[::-1][:10]
        
        axes[1, 0].bar(range(10), feature_importance[indices])
        axes[1, 0].set_title('前10个重要特征')
        axes[1, 0].set_ylabel('重要性')
        axes[1, 0].set_xticks(range(10))
        axes[1, 0].set_xticklabels([self.feature_names[i][:10] for i in indices], rotation=45)
        
        # 4. 混淆矩阵
        best_model_name = max(self.models.keys(), key=lambda x: self.models[x]['accuracy'])
        best_predictions = self.models[best_model_name]['predictions']
        cm = confusion_matrix(self.y_test, best_predictions)
        
        im = axes[1, 1].imshow(cm, interpolation='nearest', cmap='Blues')
        axes[1, 1].set_title(f'混淆矩阵 - {best_model_name}')
        
        # 添加数值标注
        for i in range(cm.shape[0]):
            for j in range(cm.shape[1]):
                axes[1, 1].text(j, i, str(cm[i, j]), ha='center', va='center')
        
        axes[1, 1].set_xticks(range(len(self.target_names)))
        axes[1, 1].set_yticks(range(len(self.target_names)))
        axes[1, 1].set_xticklabels(self.target_names)
        axes[1, 1].set_yticklabels(self.target_names)
        
        plt.tight_layout()
        plt.savefig('model_comprehensive_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印详细报告
        print("\n=== 红酒分类模型性能报告 ===")
        print(f"数据集大小: {self.X.shape[0]} 样本, {self.X.shape[1]} 特征")
        print(f"类别数量: {len(self.target_names)}")
        print(f"训练集: {self.X_train.shape[0]} 样本")
        print(f"测试集: {self.X_test.shape[0]} 样本")
        print("\n模型性能对比:")
        print("-" * 60)
        
        for name, results in self.models.items():
            print(f"{name}:")
            print(f"  测试准确率: {results['accuracy']:.4f}")
            if 'cv_mean' in results:
                print(f"  交叉验证: {results['cv_mean']:.4f} ± {results['cv_std']:.4f}")
            if 'training_time' in results:
                print(f"  训练时间: {results['training_time']:.4f} 秒")
            print()
            
    def run_complete_analysis(self):
        """运行完整分析流程"""
        print("开始红酒分类完整分析流程...")
        print("=" * 50)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 探索性数据分析
        self.exploratory_data_analysis()
        
        # 3. 数据预处理
        self.preprocess_data()
        
        # 4. 训练模型
        self.train_models()
        
        # 5. 优化SVM
        self.optimize_svm()
        
        # 6. 可视化结果
        self.visualize_results()
        
        # 7. 特征重要性分析
        self.feature_importance_analysis()
        
        # 8. 降维可视化
        self.dimensionality_reduction()
        
        # 9. 生成综合报告
        self.generate_comprehensive_report()
        
        print("\n分析完成！所有图表已保存到当前目录。")

if __name__ == "__main__":
    # 创建分类器实例并运行完整分析
    classifier = WineClassifier()
    classifier.run_complete_analysis()
