#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
红酒数据预处理和特征工程
Wine Data Preprocessing and Feature Engineering

专门用于数据预处理、特征工程和数据可视化的模块
"""

import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.datasets import load_wine
from sklearn.preprocessing import StandardScaler, MinMaxScaler, RobustScaler
from sklearn.feature_selection import SelectKBest, f_classif, RFE
from sklearn.decomposition import PCA
from sklearn.manifold import TSNE
from sklearn.ensemble import RandomForestClassifier
from scipy import stats
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei']
plt.rcParams['axes.unicode_minus'] = False

class WineDataProcessor:
    """红酒数据预处理类"""
    
    def __init__(self):
        self.data = None
        self.df = None
        self.feature_names = None
        self.target_names = None
        self.scalers = {}
        
    def load_data(self):
        """加载红酒数据集"""
        print("加载红酒数据集...")

        # 定义特征名称
        self.feature_names = [
            'alcohol', 'malic_acid', 'ash', 'alcalinity_of_ash', 'magnesium',
            'total_phenols', 'flavanoids', 'nonflavanoid_phenols', 'proanthocyanins',
            'color_intensity', 'hue', 'od280_od315_of_diluted_wines', 'proline'
        ]

        # 定义类别名称
        self.target_names = ['class_1', 'class_2', 'class_3']

        try:
            # 读取本地wine数据文件
            wine_df = pd.read_csv('wine/wine.data', header=None)

            # 第一列是类别标签，其余是特征
            y = wine_df.iloc[:, 0].values - 1  # 转换为0,1,2
            X = wine_df.iloc[:, 1:].values

            # 创建DataFrame
            self.df = pd.DataFrame(X, columns=self.feature_names)
            self.df['target'] = y
            self.df['wine_class'] = [self.target_names[i] for i in y]

        except FileNotFoundError:
            print("未找到wine/wine.data文件，使用sklearn内置数据集...")
            # 备用方案：使用sklearn内置数据集
            wine_data = load_wine()
            self.data = wine_data
            self.feature_names = wine_data.feature_names
            self.target_names = wine_data.target_names

            # 创建DataFrame
            self.df = pd.DataFrame(wine_data.data, columns=self.feature_names)
            self.df['target'] = wine_data.target
            self.df['wine_class'] = [self.target_names[i] for i in wine_data.target]

        print(f"数据集形状: {self.df.shape}")
        print(f"特征数量: {len(self.feature_names)}")
        print(f"类别: {self.target_names}")
        
    def basic_statistics(self):
        """基本统计信息"""
        print("\n=== 基本统计信息 ===")
        print("数据集概览:")
        print(self.df.describe())
        
        print("\n类别分布:")
        print(self.df['wine_class'].value_counts())
        
        print("\n缺失值检查:")
        print(self.df.isnull().sum().sum(), "个缺失值")
        
        # 检查异常值
        print("\n异常值检查 (使用IQR方法):")
        outliers_count = 0
        for feature in self.feature_names:
            Q1 = self.df[feature].quantile(0.25)
            Q3 = self.df[feature].quantile(0.75)
            IQR = Q3 - Q1
            lower_bound = Q1 - 1.5 * IQR
            upper_bound = Q3 + 1.5 * IQR
            outliers = self.df[(self.df[feature] < lower_bound) | (self.df[feature] > upper_bound)]
            if len(outliers) > 0:
                outliers_count += len(outliers)
                print(f"{feature}: {len(outliers)} 个异常值")
        
        if outliers_count == 0:
            print("未发现异常值")
            
    def plot_data_distribution(self):
        """绘制数据分布图"""
        print("\n生成数据分布可视化...")
        
        # 1. 特征分布直方图
        n_features = len(self.feature_names)
        n_cols = 4
        n_rows = (n_features + n_cols - 1) // n_cols
        
        plt.figure(figsize=(20, 5 * n_rows))
        for i, feature in enumerate(self.feature_names):
            plt.subplot(n_rows, n_cols, i + 1)
            
            # 为每个类别绘制直方图
            for j, wine_class in enumerate(self.target_names):
                data = self.df[self.df['wine_class'] == wine_class][feature]
                plt.hist(data, alpha=0.7, label=wine_class, bins=20)
            
            plt.title(f'{feature} 分布')
            plt.xlabel(feature)
            plt.ylabel('频次')
            plt.legend()
            
        plt.tight_layout()
        plt.savefig('feature_distributions_detailed.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 2. 箱线图
        plt.figure(figsize=(20, 5 * n_rows))
        for i, feature in enumerate(self.feature_names):
            plt.subplot(n_rows, n_cols, i + 1)
            
            # 准备箱线图数据
            data_by_class = [self.df[self.df['wine_class'] == wine_class][feature] 
                           for wine_class in self.target_names]
            
            box_plot = plt.boxplot(data_by_class, labels=self.target_names, patch_artist=True)
            
            # 设置颜色
            colors = ['lightblue', 'lightgreen', 'lightcoral']
            for patch, color in zip(box_plot['boxes'], colors):
                patch.set_facecolor(color)
                
            plt.title(f'{feature} 箱线图')
            plt.xticks(rotation=45)
            
        plt.tight_layout()
        plt.savefig('feature_boxplots_detailed.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def correlation_analysis(self):
        """相关性分析"""
        print("\n进行相关性分析...")
        
        # 计算相关性矩阵
        correlation_matrix = self.df[self.feature_names].corr()
        
        # 绘制相关性热力图
        plt.figure(figsize=(16, 14))
        mask = np.triu(np.ones_like(correlation_matrix, dtype=bool))
        
        sns.heatmap(correlation_matrix, mask=mask, annot=True, cmap='coolwarm', 
                   center=0, square=True, fmt='.2f', cbar_kws={"shrink": .8})
        plt.title('特征相关性热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig('correlation_heatmap.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 找出高相关性特征对
        print("\n高相关性特征对 (|r| > 0.8):")
        high_corr_pairs = []
        for i in range(len(correlation_matrix.columns)):
            for j in range(i+1, len(correlation_matrix.columns)):
                corr_value = correlation_matrix.iloc[i, j]
                if abs(corr_value) > 0.8:
                    feature1 = correlation_matrix.columns[i]
                    feature2 = correlation_matrix.columns[j]
                    high_corr_pairs.append((feature1, feature2, corr_value))
                    print(f"{feature1} - {feature2}: {corr_value:.3f}")
        
        if not high_corr_pairs:
            print("未发现高相关性特征对")
            
    def feature_scaling_comparison(self):
        """特征缩放方法对比"""
        print("\n对比不同的特征缩放方法...")
        
        # 选择几个代表性特征进行对比
        sample_features = self.feature_names[:4]
        
        # 定义不同的缩放器
        scalers = {
            '原始数据': None,
            '标准化': StandardScaler(),
            '最小-最大缩放': MinMaxScaler(),
            '鲁棒缩放': RobustScaler()
        }
        
        fig, axes = plt.subplots(len(sample_features), len(scalers), 
                                figsize=(16, 12))
        
        for i, feature in enumerate(sample_features):
            for j, (scaler_name, scaler) in enumerate(scalers.items()):
                ax = axes[i, j]
                
                if scaler is None:
                    data = self.df[feature]
                else:
                    data = scaler.fit_transform(self.df[[feature]]).flatten()
                
                ax.hist(data, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
                ax.set_title(f'{feature}\n{scaler_name}')
                ax.set_ylabel('频次')
                
        plt.tight_layout()
        plt.savefig('scaling_methods_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
    def feature_selection_analysis(self):
        """特征选择分析"""
        print("\n进行特征选择分析...")
        
        X = self.df[self.feature_names]
        y = self.df['target']
        
        # 1. 单变量特征选择 (F检验)
        selector_f = SelectKBest(score_func=f_classif, k='all')
        selector_f.fit(X, y)
        
        f_scores = selector_f.scores_
        f_scores_df = pd.DataFrame({
            'feature': self.feature_names,
            'f_score': f_scores
        }).sort_values('f_score', ascending=False)
        
        # 2. 基于随机森林的特征重要性
        rf = RandomForestClassifier(n_estimators=100, random_state=42)
        rf.fit(X, y)
        
        rf_importance = rf.feature_importances_
        rf_importance_df = pd.DataFrame({
            'feature': self.feature_names,
            'importance': rf_importance
        }).sort_values('importance', ascending=False)
        
        # 绘制特征重要性对比图
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))
        
        # F检验分数
        ax1.barh(range(len(f_scores_df)), f_scores_df['f_score'], color='lightblue')
        ax1.set_yticks(range(len(f_scores_df)))
        ax1.set_yticklabels(f_scores_df['feature'])
        ax1.set_xlabel('F检验分数')
        ax1.set_title('基于F检验的特征重要性')
        ax1.invert_yaxis()
        
        # 随机森林重要性
        ax2.barh(range(len(rf_importance_df)), rf_importance_df['importance'], color='lightgreen')
        ax2.set_yticks(range(len(rf_importance_df)))
        ax2.set_yticklabels(rf_importance_df['feature'])
        ax2.set_xlabel('重要性分数')
        ax2.set_title('基于随机森林的特征重要性')
        ax2.invert_yaxis()
        
        plt.tight_layout()
        plt.savefig('feature_importance_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印前10个重要特征
        print("\n前10个重要特征 (F检验):")
        for i, row in f_scores_df.head(10).iterrows():
            print(f"{row['feature']}: {row['f_score']:.2f}")
            
        print("\n前10个重要特征 (随机森林):")
        for i, row in rf_importance_df.head(10).iterrows():
            print(f"{row['feature']}: {row['importance']:.4f}")
            
    def dimensionality_reduction_analysis(self):
        """降维分析"""
        print("\n进行降维分析...")
        
        X = self.df[self.feature_names]
        y = self.df['target']
        
        # 标准化数据
        scaler = StandardScaler()
        X_scaled = scaler.fit_transform(X)
        
        # PCA分析
        pca = PCA()
        X_pca = pca.fit_transform(X_scaled)
        
        # 绘制PCA分析图
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        
        # 1. 方差解释比例
        axes[0, 0].plot(range(1, len(pca.explained_variance_ratio_) + 1), 
                       pca.explained_variance_ratio_, 'bo-')
        axes[0, 0].set_xlabel('主成分')
        axes[0, 0].set_ylabel('方差解释比例')
        axes[0, 0].set_title('PCA方差解释比例')
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 累积方差解释比例
        cumsum_var = np.cumsum(pca.explained_variance_ratio_)
        axes[0, 1].plot(range(1, len(cumsum_var) + 1), cumsum_var, 'ro-')
        axes[0, 1].axhline(y=0.95, color='k', linestyle='--', alpha=0.7, label='95%')
        axes[0, 1].set_xlabel('主成分')
        axes[0, 1].set_ylabel('累积方差解释比例')
        axes[0, 1].set_title('PCA累积方差解释比例')
        axes[0, 1].legend()
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. PCA 2D可视化
        colors = ['red', 'green', 'blue']
        for i, target_name in enumerate(self.target_names):
            mask = y == i
            axes[1, 0].scatter(X_pca[mask, 0], X_pca[mask, 1], 
                             c=colors[i], label=target_name, alpha=0.7)
        axes[1, 0].set_xlabel(f'PC1 ({pca.explained_variance_ratio_[0]:.2%})')
        axes[1, 0].set_ylabel(f'PC2 ({pca.explained_variance_ratio_[1]:.2%})')
        axes[1, 0].set_title('PCA 2D可视化')
        axes[1, 0].legend()
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. t-SNE可视化
        tsne = TSNE(n_components=2, random_state=42, perplexity=30)
        X_tsne = tsne.fit_transform(X_scaled)
        
        for i, target_name in enumerate(self.target_names):
            mask = y == i
            axes[1, 1].scatter(X_tsne[mask, 0], X_tsne[mask, 1], 
                             c=colors[i], label=target_name, alpha=0.7)
        axes[1, 1].set_xlabel('t-SNE 1')
        axes[1, 1].set_ylabel('t-SNE 2')
        axes[1, 1].set_title('t-SNE 2D可视化')
        axes[1, 1].legend()
        axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig('dimensionality_reduction_analysis.png', dpi=300, bbox_inches='tight')
        plt.show()
        
        # 打印PCA信息
        print(f"\n前5个主成分解释的方差比例: {pca.explained_variance_ratio_[:5]}")
        print(f"前5个主成分累积解释方差: {cumsum_var[4]:.3f}")
        
        # 找出需要多少个主成分来解释95%的方差
        n_components_95 = np.argmax(cumsum_var >= 0.95) + 1
        print(f"解释95%方差需要的主成分数量: {n_components_95}")
        
    def generate_preprocessing_report(self):
        """生成预处理报告"""
        print("\n" + "="*60)
        print("红酒数据预处理分析报告")
        print("="*60)
        
        print(f"数据集大小: {self.df.shape[0]} 样本, {self.df.shape[1]-2} 特征")
        print(f"类别数量: {len(self.target_names)}")
        print(f"类别分布: {dict(self.df['wine_class'].value_counts())}")
        
        print(f"\n数据质量:")
        print(f"- 缺失值: {self.df.isnull().sum().sum()} 个")
        print(f"- 数据类型: 全部为数值型")
        print(f"- 特征范围: 各特征量纲差异较大，建议进行标准化")
        
        print(f"\n建议的预处理步骤:")
        print(f"1. 特征标准化 (StandardScaler)")
        print(f"2. 特征选择 (保留前10-12个重要特征)")
        print(f"3. 降维 (PCA保留95%方差，约需要{np.argmax(np.cumsum(PCA().fit(StandardScaler().fit_transform(self.df[self.feature_names])).explained_variance_ratio_) >= 0.95) + 1}个主成分)")
        
    def run_complete_preprocessing(self):
        """运行完整的数据预处理分析"""
        print("开始红酒数据预处理分析...")
        print("="*50)
        
        # 1. 加载数据
        self.load_data()
        
        # 2. 基本统计
        self.basic_statistics()
        
        # 3. 数据分布可视化
        self.plot_data_distribution()
        
        # 4. 相关性分析
        self.correlation_analysis()
        
        # 5. 特征缩放对比
        self.feature_scaling_comparison()
        
        # 6. 特征选择分析
        self.feature_selection_analysis()
        
        # 7. 降维分析
        self.dimensionality_reduction_analysis()
        
        # 8. 生成报告
        self.generate_preprocessing_report()
        
        print("\n数据预处理分析完成！所有图表已保存。")

if __name__ == "__main__":
    # 运行数据预处理分析
    processor = WineDataProcessor()
    processor.run_complete_preprocessing()
